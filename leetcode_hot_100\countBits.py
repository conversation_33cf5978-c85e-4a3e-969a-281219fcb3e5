# 338. 比特位计数
# https://leetcode.cn/problems/counting-bits//description/?envType=problem-list-v2&envId=2cktkvj

class Solution:
    def countBits(self, n: int) -> List[int]:
        """
        Given an integer n, return an array ans of length n + 1 such that for each i (0 <= i <= n), ans[i] is the number of 1's in the binary representation of i.
        """
        ans = [0] * (n + 1)
        for i in range(1, n + 1):
            ans[i] = ans[i >> 1] + (i & 1)
        return ans